import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HomeRoutingModule } from './home-routing.module';
import { MapComponent } from '../map/map.component';
import { HomeComponent } from './Component/home.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { AppRoutingModule } from '../app-routing.module';
import { GroupedDropdownComponent } from './Component/GrpDropDown.Component';
import { FormsComponent } from './Component/forms.component';
import { FormHtmlViewComponent } from './Component/form-html-view.component';
import { SharedFormViewComponent } from './Component/shared-form-view.component';
import { ZXingScannerModule } from '@zxing/ngx-scanner';
import { FormSkeletonComponent } from '../SharedData/form-skeleton.component';
import { NgChartsModule } from 'ng2-charts';
import { KeyValueToArrayPipe } from '../SharedData/key-value-to-array.pipe';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';


@NgModule({
  declarations: [
    MapComponent,
    HomeComponent,
    FormsComponent,
    FormHtmlViewComponent,
    SharedFormViewComponent,
  ],
  imports: [
    FormsModule,
    CommonModule,
    HomeRoutingModule,
    AppRoutingModule,
    ReactiveFormsModule,
    GroupedDropdownComponent,
    ZXingScannerModule,
    FormSkeletonComponent,
    NgChartsModule,
    DateInputsModule ,
    KeyValueToArrayPipe,

  ]
})
export class HomeModule { }
