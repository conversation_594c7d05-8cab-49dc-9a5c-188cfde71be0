/* Header styles */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #00457c 100%);
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  height: 50px; /* Thin header */
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 100%;
}

/* Navbar sections */
.navbar-left, .navbar-right, .navbar-middle {
  display: flex;
  align-items: center;
}

.navbar-left {
  flex: 1;
  justify-content: flex-start;
}

.navbar-middle {
  flex: 0.5;
  justify-content: center;
}

.navbar-right {
  flex: 1;
  justify-content: flex-end;
}

.logo {
  height: 43px;
  cursor: pointer;
  margin-left: 10px;
  margin-top: 15px;
  margin-bottom: 5px;
  margin-right: 10px;
  border-radius: 10px;
  width: 90px;
}

.logo-color{
  color: #00198a;
}
.nav-links {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: nowrap; /* Prevent wrapping to new line */
}

.nav-links li {
  margin-left: 20px;
}

.nav-links a {
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s;
}

.nav-links a:hover {
  color: #e0e0e0;
}

.nav-links a.active {
  font-weight: bold;
  border-bottom: 2px solid white;
}

/* User profile and dropdown styles */
.user-profile {
  position: relative;
  margin-left: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-profile i {
  margin-right: 5px;
}

.logout-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 180px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: none;
  z-index: 1000;
  margin-top: 5px;
  overflow: hidden;
}

.user-profile:hover .logout-dropdown {
  display: block;
}

.dropdown-item {
  padding: 10px 15px;
  color: #333;
  font-size: 14px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item i {
  margin-right: 10px;
  color: #666;
  width: 16px;
  text-align: center;
}

.dropdown-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 5px 0;
}

.dropdown-item.logout {
  color: #f44336;
}

.dropdown-item.logout i {
  color: #f44336;
}

/* Location dropdown styles */
.location-dropdown {
  /* No margin needed as it's centered */
  width: 100%;
  max-width: 250px;
}

.location-selector {
  display: flex;
  align-items: center;
  border-radius: 4px;
  padding: 5px 10px;
  transition: background-color 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.location-selector:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.location-selector i {
  color: white;
  margin-right: 8px;
  font-size: 14px;
}

.location-text {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.location-arrow {
  font-size: 12px;
  color: white;
}

/* Location Popup Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1100;
  display: flex;
  justify-content: center;
  align-items: center;
}

.location-popup-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f8f8;
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: #eee;
}

.popup-content {
  padding: 15px;
  overflow-y: auto;
  max-height: 60vh;
}

.location-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.location-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.location-item:hover {
  background-color: #f5f5f5;
}

.location-item.selected {
  background-color: #e3f2fd;
  border-left: 3px solid #2196F3;
}

.location-icon {
  margin-right: 12px;
  color: #2196F3;
  width: 20px;
  display: flex;
  justify-content: center;
}

.location-name {
  font-size: 16px;
  color: #333;
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
  display: none;
  cursor: pointer;
  font-size: 24px;
  color: white;
  margin-left: auto;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
  position: relative;
  z-index: 10;
}

.mobile-menu-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  max-width: 320px;
  height: 100vh;
  background-color: white;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1200;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-menu-overlay.open {
  right: 0;
}

.mobile-menu-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #3f51b5;
  color: white;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
}

.mobile-user-info i {
  font-size: 20px;
}

.mobile-menu-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-body {
  padding: 15px 0;
  flex: 1;
}

.mobile-nav-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-links li {
  margin: 0;
}

.mobile-nav-links a {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #333;
  text-decoration: none;
  font-size: 16px;
  transition: background-color 0.2s;
}

.mobile-nav-links a:hover {
  background-color: #f5f5f5;
}

.mobile-nav-links a i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
  color: #666;
}

.mobile-nav-links .divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 10px 0;
}

.mobile-nav-links .logout {
  color: #f44336;
}

.mobile-nav-links .logout i {
  color: #f44336;
}

/* Mobile Location Dropdown - Base Styles */
.mobile-location-dropdown {
  display: none; /* Hidden by default, shown in mobile view */
}

/* Responsive styles for navbar */
@media (max-width: 768px) {
  .navbar {
    padding: 0 10px;
  }

  .navbar-middle {
    flex: 1;
    justify-content: center;
  }

  /* Show mobile location dropdown in navbar middle */
  .mobile-location-dropdown {
    display: block;
    width: 100%;
    max-width: 200px;
  }

  .mobile-location-dropdown .location-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    font-size: 13px;
  }

  .mobile-location-dropdown .location-selector:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .mobile-location-dropdown .location-text {
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0 8px;
    font-weight: 500;
  }

  .mobile-location-dropdown .location-arrow {
    font-size: 10px;
    opacity: 0.8;
  }

  /* Hide desktop location dropdown */
  .location-dropdown-item {
    display: none !important;
  }

  .mobile-menu-toggle {
    display: block;
    flex: 0;
    text-align: right;
  }

  .navbar-right {
    display: none;
  }

  /* Adjust sidebar and main content for tablet */
  .left-sidebar {
    width: 45px;
  }

  .sidebar-nav a {
    padding: 10px 6px;
  }

  .sidebar-nav i {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .navbar-left {
    flex: 0.3;
  }

  .navbar-middle {
    flex: 1;
    justify-content: center;
  }

  /* Mobile location dropdown for smaller screens */
  .mobile-location-dropdown {
    display: block;
    width: 100%;
    max-width: 180px;
  }

  .mobile-location-dropdown .location-selector {
    padding: 4px 8px;
    font-size: 12px;
  }

  .mobile-location-dropdown .location-text {
    max-width: 100px;
    font-size: 0.75rem;
  }

  .mobile-location-dropdown .location-arrow {
    display: none;
  }

  /* Hide desktop location dropdown */
  .location-dropdown-item {
    display: none !important;
  }

  /* Adjust sidebar for mobile - icon only mode */
  .left-sidebar {
    width: 35px;
  }

  .sidebar-nav a {
    padding: 8px 4px;
  }

  .sidebar-nav i {
    font-size: 14px;
  }
}

/* App Container Layout */
.app-container {
  display: flex;
  height: calc(100vh - 58px); /* Subtract header height */
  margin-top: 50px; /* Add margin to prevent content from being hidden under the header */
  overflow: hidden;
}

/* Left Sidebar Styles */
.left-sidebar {
  width: 50px;
  background: white;
  border-right: 1px solid #e0e0e0;
  height: 100%;
  overflow-y: auto;
  flex-shrink: 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  background-color: #3f51b5;
  color: white;
  display: none; /* Hidden as per your HTML comment */
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.sidebar-content {
  padding: 10px 0;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 4px;
  position: relative;
  white-space: nowrap;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  color: #555;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 2px 8px;
  position: relative;
}

.sidebar-nav a:hover {
  background: #f5f5f5;
  color: #2196F3;
}

.sidebar-nav a.active {
  background: #e3f2fd;
  color: #2196F3;
  border-left: 3px solid #2196F3;
  padding-left: 5px;
}

.sidebar-nav i {
  margin-right: 0;
  width: 20px;
  text-align: center;
  font-size: 18px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  color: #666;
}

.sidebar-nav a:hover i {
  color: #2196F3;
  transform: scale(1.1);
}

.sidebar-nav span {
  display: none;
}

.sidebar-nav a.active i {
  color: #2196F3;
}

.sidebar-divider {
  height: 1px;
  background-color: #ddd;
  margin: 5px 0;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #fff;
  transition: margin-left 0.3s ease;
}

/* Full width style for when sidebar is hidden (not logged in) */
.main-content.full-width {
  margin-left: 0;
  width: 100%;
}

/* Responsive adjustments for the sidebar are defined in the main responsive section */

/* Legacy content styles (keeping for compatibility) */
.content {
  margin-top: 50px;
  padding: 0px;
}
