import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { ShareService } from './SharedData/share-services.service';
import { MsalService } from '@azure/msal-angular';
import { AuthService } from './Authentication/Services/auth.service';
import { User } from './Authentication/Model/auth.model';
import { forkJoin } from 'rxjs';
import { CoreDataService } from './core-data.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  title = 'FORM';

  selectionList: string[] = []; //locations which is Show in popUp

  currentUser: User | null = null;
  isLoggedIn = true;
  isFormPreviewRoute = false;
  isFormEditMode = false;

  constructor(
    private router: Router,
    protected shareServices: ShareService,
    private coreData: CoreDataService,
    private msalService: MsalService,
    public authService: AuthService
  ) {
    // Subscribe to auth state changes
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      // this.isLoggedIn = !!user;
    });

    this.coreData.getLocation().subscribe({
      next:(res)=>{
        const loc = res.map((item: any) => item.name);
        this.selectionList = loc;
      }
    })
  }
  // showLocationPopup: boolean = false;
  isMobileMenuOpen: boolean = false;
  activeRoute: string = '';




  ngOnInit() {
    // Try to auto-login the user if they have a valid token
    this.authService.autoLogin().subscribe(success => {
      if (success) {
        console.log('✅ Auto login successful');
      }
    });

    this.msalService.instance.handleRedirectPromise().then(result => {
      if (result?.account) {
        console.log('✅ Login successful:', result.account);
        this.msalService.instance.setActiveAccount(result.account);
      } else {
        const accounts = this.msalService.instance.getAllAccounts();
        if (accounts.length > 0) {
          this.msalService.instance.setActiveAccount(accounts[0]);
        } else {
          console.log('🟥 No account found after redirect.');
        }
      }
    }).catch(error => {
      console.error('🛑 Error handling redirect:', error);
    });

    // Track current route for sidebar active state
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.activeRoute = event.url;

        // Check if we're on the form-preview route
        this.isFormPreviewRoute = event.url.includes('/form-preview');
        this.isFormEditMode = event.url.includes('/form-edit');

        // Show location popup if at form routes and no location is selected
        if ((event.url === '/form-view' || this.router.url === '/form-view') && this.shareServices.selectedLocation === ''
      && !this.shareServices.role) {
          this.openLocationPopup();

        }
        if(event.url === '/admin'){
          this.shareServices.role = true;
        }
      }
    });

    // Check if we're already at the form routes when the app starts
    // if (this.router.url === '/form-view' || !this.shareServices.role) {
      // this.openLocationPopup();
    // }
  }

  openLocationPopup() {
    // Only show the location popup if no location is already selected
    if (this.shareServices.selectedLocation === '') {
      this.shareServices.showLocationPopup = true;
    }

  }
  openLocationbyUSer() {
    this.shareServices.showLocationPopup = true;
  }

  closeLocationPopup() {
    this.shareServices.showLocationPopup = false;
  }

  toggleUser() {
    // this.shareServices.role = !this.shareServices.role;
    if(this.shareServices.role){ //this will change View In User*******
      this.router.navigate(['/form-view']);
      this.openLocationPopup();
      this.shareServices.role = false;
    }else{     //this will change View In Admin*******
      this.router.navigate(['/admin']);
      this.shareServices.role = true;
    }
  }

  onLocationChange() {
    console.log('Selected location:', this.shareServices.selectedLocation);
    // You can add additional logic here to handle location changes
    // For example, you might want to store the selected location in a service
   forkJoin({
      formList: this.coreData.getAllforms(),
      locationList: this.coreData.getallFormList()
    }).subscribe({
      next: ({ formList, locationList }) => {
        this.shareServices.FromList = formList;
        this.shareServices.lists = locationList;

        this.closeLocationPopup(); // only after both API calls finish
      },
      error: (err) => {
        console.error('Error fetching data:', err);
      }
    });
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    // Prevent scrolling when menu is open
    if (this.isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
    document.body.style.overflow = '';
  }

  logout() {
    this.authService.logout();
    this.closeMobileMenu();
  }
}
