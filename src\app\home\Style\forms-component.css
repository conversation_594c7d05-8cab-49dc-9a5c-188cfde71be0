/* Container for the entire forms page */
.forms-container {
  display: flex;
  height: calc(100vh - 60px); /* Adjust based on your header height */
  position: relative;
}

/* Sidebar styles */
.sidebar {
  width: 250px;
  background-color: #f5f5f5;
  border-right: 1px solid #ddd;
  transition: all 0.3s ease;
  overflow-y: auto;
  height: 100%;
}

.sidebar.collapsed {
  width: 0;
  overflow: hidden;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid #ddd;
  background: linear-gradient(212deg, #667eea 0%, #137fd4 100%);
  color: white;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.sidebar-content {
  padding: 5px 0;
}

.forms-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.form-item {
  margin-bottom: 2px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.form-header:hover {
  background-color: #e0e0e0;
}

.form-header.active {
  background-color: #e8eaf6;
  border-left: 4px solid #3f51b5;
  padding-left: 11px; /* 15px - 4px border */
}

.form-name {
  display: block;
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* Form Types List */
.form-types-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.form-type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #e5e5e5;
  transition: background-color 0.2s;
  font-size: 0.9rem;
}

.form-type-item:hover {
  background-color: #e0e0e0;
}

.form-type-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Form Submissions List */
.submissions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.submission-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #e5e5e5;
  transition: background-color 0.2s;
}

.submission-item:hover {
  background-color: #e0e0e0;
}

.submission-item.active {
  background-color: #e8eaf6;
  border-left: 4px solid #3f51b5;
  padding-left: 11px;
}

.submission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.submission-id {
  font-size: 0.8rem;
  font-weight: 500;
  color: #333;
}

.submission-date {
  font-size: 0.7rem;
  color: #666;
}

.submission-info {
  font-size: 0.75rem;
  color: #555;
}

.submission-by {
  display: block;
}

/* Back button in header */
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  color: #e0e0e0;
}

.no-forms {
  padding: 15px;
  color: #757575;
  font-style: italic;
}

/* Main content styles */
.main-content {
  flex: 1;
  padding: 0px;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.main-content.expanded {
  flex: 1;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #757575;
  font-style: italic;
}

/* Sidebar toggle button */
.sidebar-toggle {
  position: absolute;
  left: 250px; /* Same as sidebar width */
  top: 10px;
  z-index: 100;
  background-color: #3f51b5;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 8px 12px;
  cursor: pointer;
  transition: left 0.3s ease;
}

.sidebar-toggle:hover {
  background-color: #303f9f;
}

.sidebar-toggle i {
  font-size: 14px;
}

/* When sidebar is collapsed, move the toggle button */
.sidebar.collapsed ~ .main-content .sidebar-toggle {
  left: 0;
}

/* Mobile View ********************************/
.bottom-nav {
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #ddd;
  padding: 8px 0;
  z-index: 999;
}

.nav-button {
  flex: 1;
  border: none;
  background: none;
  font-size: 12px;
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-button.active {
  color: #3f51b5;
}

.nav-button i {
  font-size: 18px;
}
