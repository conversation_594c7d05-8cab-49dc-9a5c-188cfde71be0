import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Subject } from 'rxjs';
import { DropdownData, FormJson } from '../home/<USER>/model';
import { Configuration } from '@azure/msal-browser';


@Injectable({
  providedIn: 'root'
})
export class ShareService {

  constructor(private toast: ToastrService) { }
  showLocationPopup: boolean = false;

  selectedLocation: string = ''; // select Location in Header

  FromList: FormJson[]= []; //forms

  lists: DropdownData[]=[]; //Lists

  showSkeleton: boolean = false;

  // temprory role decided
  role: boolean = false;

  formsListforBuilder: FormJson[] = []; //for show forms List in form Builder
  // Subject for unload DashBoard via Logo
  private unloadDashBoard = new Subject<void>();

  public unloadDashBoard$ = this.unloadDashBoard.asObservable();

  public HideDashBoard() {
    this.unloadDashBoard.next();
  }

  private showDashoard = new Subject<void>();
  public showDashBoard$ = this.showDashoard.asObservable();
  public ShowDashBoard(){
    this.showDashoard.next();
  }

  // Subject to trigger full window mode for form HTML view
  private toggleFullWindowModeSubject = new Subject<void>();

  public toggleFullWindowMode$ = this.toggleFullWindowModeSubject.asObservable();


  // Subject for sidebar view changes
  private sidebarViewChangeSubject = new Subject<'forms' | 'types' | 'submissions'>();

  sidebarViewChange$ = this.sidebarViewChangeSubject.asObservable();



  // Method to change sidebar view
  changeSidebarView(view: 'forms' | 'types' | 'submissions') {
    this.sidebarViewChangeSubject.next(view);
  }

  showSuccess(message: string, title?: string) {
    this.toast.success(message, title);
  }

  showError(message: string, title?: string) {
    this.toast.error(message, title);
  }

  showWarning(message: string, title?: string) {
    this.toast.warning(message, title);
  }

  showInfo(message: string, title?: string) {
    this.toast.info(message, title);
  }
}
