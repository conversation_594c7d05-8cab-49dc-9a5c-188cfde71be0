<header class="app-header" *ngIf="!isFormPreviewRoute">
  <nav class="navbar">
    <!-- Left section: Logo (click)="hideDashboard()" -->
    <div class="navbar-left">
      <div ><img class="logo" routerLink="/admin"  src="assets/Images/Forms flow.png" alt="Logo"></div>
    </div>

    <!-- Middle section: Location Button -->
    <div class="navbar-middle" *ngIf="isLoggedIn && !isFormEditMode && !this.shareServices.role">
      <!-- Location Dropdown for Mobile View -->
      <div class="mobile-location-dropdown">
        <div class="location-selector" (click)="openLocationbyUSer()">
          <i class="fas fa-map-marker-alt"></i>
          <span class="location-text">{{ this.shareServices.selectedLocation }}</span>
          <i class="fas fa-chevron-down location-arrow"></i>
        </div>
      </div>
    </div>

    <!-- Mobile Menu Toggle Button -->
    <div class="mobile-menu-toggle" (click)="toggleMobileMenu()">
      <i class="fas" [ngClass]="{'fa-bars': !isMobileMenuOpen, 'fa-times': isMobileMenuOpen}"></i>
    </div>

    <!-- Right section: Navigation links (Desktop) -->
    <div class="navbar-right">
      <ul class="nav-links">

        <!-- Location Dropdown -->
        <li class="location-dropdown-item" *ngIf="isLoggedIn && !isFormEditMode && !this.shareServices.role">
          <div class="location-dropdown">
            <div class="location-selector" (click)="openLocationbyUSer()">
              <i class="fas fa-map-marker-alt"></i>
              <span class="location-text">{{ this.shareServices.selectedLocation }}</span>
              <i class="fas fa-chevron-down location-arrow"></i>
            </div>
          </div>
        </li>

        <!-- Show user profile if logged in -->
        <li class="user-profile" *ngIf="isLoggedIn && !isFormEditMode">
          <div class="user-info">
            <i class="fas fa-user"></i> {{ currentUser?.firstName || 'Dev One' }}
          </div>



          <div class="logout-dropdown">
            <div class="dropdown-item" (click)="toggleUser()">
              <i class="fas fa-sign-out-alt"></i> {{this.shareServices.role ? 'User' : 'Admin'}}
            </div>
            <div class="dropdown-item">
              <i class="fas fa-user-circle"></i> My Profile
            </div>
            <div class="dropdown-item">
              <i class="fas fa-cog"></i> Settings
            </div>
            <div class="dropdown-divider"></div>
            <div class="dropdown-item logout" (click)="logout()">
              <i class="fas fa-sign-out-alt"></i> Logout
            </div>
          </div>
        </li>
      </ul>
    </div>
  </nav>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-menu-overlay" [class.open]="isMobileMenuOpen">
    <div class="mobile-menu-content">
      <div class="mobile-menu-header">
        <div class="mobile-user-info" *ngIf="isLoggedIn && !isFormEditMode">
          <i class="fas fa-user-circle"></i>
          <span>{{ currentUser?.firstName || 'User' }}</span>
        </div>
        <div class="mobile-user-info" *ngIf="!isLoggedIn">
          <i class="fas fa-user-circle"></i>
          <span>Dev One</span>
        </div>
        <button type="button" class="mobile-menu-close" (click)="toggleMobileMenu()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="mobile-menu-body">
        <ng-container *ngIf="isLoggedIn">
        <ul class="mobile-nav-links">
          <!-- Auth links for non-logged in users -->
          <!-- <ng-container *ngIf="!isLoggedIn">
            <li><a routerLink="/auth/login" (click)="closeMobileMenu()">Login</a></li>
            <li><a routerLink="/auth/signup" (click)="closeMobileMenu()">Sign Up</a></li>
            <li class="divider"></li>
          </ng-container> -->

          <!-- App links for logged in users -->
            <li><a routerLink="/build/form" (click)="closeMobileMenu()">Build Form</a></li>
            <li><a routerLink="/build/List" (click)="closeMobileMenu()">Form List</a></li>
            <li class="divider"></li>
            <li><a href="#"><i class="fas fa-user-circle"></i> My Profile</a></li>
            <li><a href="#"><i class="fas fa-cog"></i> Settings</a></li>
            <li><a (click)="logout()" class="logout"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
          </ul>
          </ng-container>
      </div>
    </div>
  </div>

  <!-- Location Popup -->
  <div class="popup-overlay" *ngIf="this.shareServices.showLocationPopup" >
    <div class="location-popup-container" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>Select Location</h3>
        <!-- <button class="close-btn" (click)="closeLocationPopup()">×</button> -->
      </div>
      <div class="popup-content">
        <div class="location-list">
          <div
            *ngFor="let location of selectionList"
            class="location-item"
            [class.selected]="location === shareServices.selectedLocation"
            (click)="shareServices.selectedLocation = location; onLocationChange()">
            <div class="location-icon">
              <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="location-name">{{ location }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<div class="app-container">
  <!-- Left Sidebar - Only show when logged in and not on form-preview route -->
  <aside class="left-sidebar" *ngIf="isLoggedIn && !isFormPreviewRoute && !isFormEditMode">
    <div class="sidebar-content">
      <ul class="sidebar-nav">
        <li title="Dashboard" *ngIf="this.shareServices.role">
          <a routerLink="/admin" routerLinkActive="active">
            <i class="fas fa-tachometer-alt" ></i>
            <span>Dashboard</span>
          </a>
        </li>
        <li title="Form config" *ngIf="this.shareServices.role">
          <a routerLink="/form/admin/config" routerLinkActive="active">
            <i class="fa-solid fa-toolbox" title="Form config"></i>
            <span>Form config</span>
          </a>
        </li>
        <li title="Company Setting" *ngIf="this.shareServices.role">
          <a routerLink="/Company/admin/integration" routerLinkActive="active">
            <i class="fas fa-cog" title="Company Setting"></i>
            <span>Company Setting</span>
          </a>
        </li>
        <li title="Locations" *ngIf="this.shareServices.role">
          <a routerLink="/Locations" routerLinkActive="active">
            <i class="fas fa-map-marker-alt" title="Locations"></i>
            <span>Locations</span>
          </a>
        </li>
        <li title="Users" *ngIf="this.shareServices.role">
          <a routerLink="/Users" routerLinkActive="active">
            <i class="fas fa-users" title="Users"></i>
            <span>Users</span>
          </a>
        </li>
        <li title="User Forms" *ngIf="!this.shareServices.role">
          <a routerLink="/form-view" routerLinkActive="active" >
            <i class="fa-solid fa-newspaper"></i>
            <span>Users forms</span>
          </a>
        </li>
      </ul>
    </div>
  </aside>

  <!-- Main Content - Adjust width based on login status and route -->
  <main class="main-content" [ngClass]="{'full-width': !isLoggedIn || isFormPreviewRoute || isFormEditMode}">
    <router-outlet></router-outlet>
  </main>
</div>

<!-- #DashBoard -->
