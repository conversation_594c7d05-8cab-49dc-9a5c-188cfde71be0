import { ShareService } from './../../SharedData/share-services.service';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CoreDataService } from '../../core-data.service';
import { FormJson, FormSection, FormComponent, DropdownData } from '../../home/<USER>/model';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-form-builder',
  templateUrl: './../Template/form-builder.component.html',
  styleUrl: './../Styles/form-builder.component.css'
})
export class FormBuilderComponent implements OnInit {
  // Form builder form
  formBuilderForm: FormGroup;

  // Form data
  formData: FormJson = {
    auditHistory:{
      formName: '',
      userName: '',
      location: '',
    },
    component:[]
  };

  // Create form modal
  showCreateFormModal: boolean = false;
  createFormForm: FormGroup;

  // Track the currently selected section for adding elements
  selectedSection: FormSection | null = null;

  // Flag to show/hide the element selection sidebar
  showElementSelection: boolean = false;

  // Flag to show URL input in the element selection modal
  showLinkUrlInput: boolean = false;

  // Flag to show dropdown lists
  showDropdownLists: boolean = false;

  // Available form lists for dropdown
  formLists: DropdownData[] = [];

  // Flag to show guide image upload
  showGuideImageUpload: boolean = false;

  // Guide image data
  guideImageUrl: string = '';

  // Filtered form lists based on search
  filteredFormLists: DropdownData[] = [];

  // Search term for filtering lists
  listSearchTerm: string = '';

  // Selected form list for dropdown
  selectedFormList: DropdownData | null = null;

  // Form list properties for sidebar layout
  selectedFormId: string | null = null;

  // Reference to the link item element
  linkItemElement: Element | null = null;

  // for Load From Data'
   formsListforBuilder: FormJson[] = [];

  //  Conditional Section
  activeConditionSection: FormSection | null = null;
  tempCondition = {
    field: '',
    operator: '==',
    value: ''
  };

  // Available field names for dropdown
  availableFields: { fieldName: string, label: string }[] = [];

  // Selected element for validation editing
  selectedElement: FormComponent | null = null;
  selectedElementSection: FormSection | null = null;
  showPdfUpload: boolean = false;

  constructor(
    private coreDataService: CoreDataService,
    private fb: FormBuilder, private route:Router,
    protected shareService:ShareService,
    private http: HttpClient
  ) {
    this.formBuilderForm = this.fb.group({
        formName: ['New Form', Validators.required],
        userName: ['Current User', Validators.required],
    });

    this.createFormForm = this.fb.group({
        formName: ['New Form', Validators.required],
        userName: ['Current User', Validators.required],
        // location: [this.shareService.selectedLocation, Validators.required],
    });
  }

  ngOnInit(): void {

    // Initialize with one empty section
    // this.addSection();
    // add header in DB_DATA
    this.formData.auditHistory.location = this.shareService.selectedLocation;
    this.loadFormData();

    // Subscribe to form value changes
    this.formBuilderForm.valueChanges.subscribe(values => {
      this.updateFormData(values);
    });
  }

  loadFormData(): void {
  forkJoin({
    forms: this.coreDataService.getAllforms(),
    formList: this.coreDataService.getallFormList()
  }).subscribe({
    next: ({ forms, formList }) => {
      this.formsListforBuilder = forms;
      this.formLists = formList;
    },
    error: (err) => {
      console.error('Error loading form data:', err);
    }
  });
}

  // Filter lists based on search term
  filterLists(): void {
    if (!this.listSearchTerm.trim()) {
      this.filteredFormLists = [...this.formLists];
      return;
    }
    console.log(this.filteredFormLists, 'filteredFormLists');
    const searchTerm = this.listSearchTerm.toLowerCase().trim();
    this.filteredFormLists = this.formLists.filter(list =>
      list.auditHistory.listName?.toLowerCase().includes(searchTerm)
    );
  }

  // Create a new form
  createNewForm(): void {
    const formValues = this.createFormForm.value;

    this.formData = {
      auditHistory: {
        formName: formValues.formName,
        userName: formValues.userName,
        location: this.shareService.selectedLocation,
      },
      component: []
    };
    this.formBuilderForm.patchValue({
      formName: formValues.formName,
      userName: formValues.userName,
    });

    // Add an initial section
    this.addSection();

    // Set selected form ID to show the form builder
    this.selectedFormId = 'new-form';

    // Close the Form pop-up
    this.showCreateFormModal = false;

    console.log('New form created:', this.formData);
  }
  // Update form data when form values change
  updateFormData(values: any): void {
    this.formData.auditHistory.formName = values.formName;
    this.formData.auditHistory.userName = values.userName;
    // console.log('Form data updated:', this.formData);
  }
  // New section
  addSection(): void {
    const newSection: FormSection = {
      title: 'Rename Section',
      canCollapsed: false, // Default to not collapsible
      isCollapsed: false,
      repeatable: false,  // Default to not repetable
      elements: []
    };
    this.formData.component.push(newSection);
  }

handleConditionToggle(section: FormSection): void {
  if (section._isConditional) {
    // Update available fields before showing popup
    this.updateAvailableFields();

    // Show popup for setting condition
    this.activeConditionSection = section;

    // Set default if previously defined
    this.tempCondition = {
      field: section.condition?.field || '',
      operator: section.condition?.operator || '==',
      value: section.condition?.value || ''
    };
  } else {
    // Clear existing condition
    delete section.condition;
    this.activeConditionSection = null;
  }
}

applyCondition(): void {
  if (this.activeConditionSection) {
    this.activeConditionSection.condition = {
      field: this.tempCondition.field,
      operator: this.tempCondition.operator as '==' | '!=',
      value: this.tempCondition.value
    };
  }
  this.activeConditionSection = null;
}

cancelCondition(): void {
  if (this.activeConditionSection) {
    // Uncheck the checkbox
    (this.activeConditionSection as any)._isConditional = false;

    // Remove the condition (optional cleanup)
    delete this.activeConditionSection.condition;
  }
  this.activeConditionSection = null;
}

// Get all available field names from the current form
updateAvailableFields(): void {
  this.availableFields = [];

  if (this.formData && this.formData.component) {
    this.formData.component.forEach(section => {
      section.elements.forEach(element => {
        if (element.attributes.field_name && element.attributes.label) {
          this.availableFields.push({
            fieldName: element.attributes.field_name,
            label: element.attributes.label
          });
        }
      });
    });
  }
}

  updateSectionTitle(section: FormSection, newTitle: string): void {
    section.title = newTitle;
  }

  // Toggle section collapse state
  toggleSectionCollapse(section: FormSection): void {
    if (section.canCollapsed) {
      section.isCollapsed = !section.isCollapsed;
    }
  }

  // Save the form
  saveForm(): void {
    console.log(JSON.stringify(this.formData, null, 2));

    // Here you would typically send the form data to your backend
    this.coreDataService.saveFormTemplate(this.formData).subscribe(response => {
        console.log('Form saved successfully', response);
        this.route.navigate(['']);
      });
  }

  // Show element selection modal for a specific section
  showElementSelectionSidebar(section: FormSection): void {
    this.selectedSection = section;
    this.showElementSelection = true;
  }

  // Hide element selection modal
  hideElementSelectionSidebar(): void {
    this.showElementSelection = false;
    this.showGuideImageUpload = false;
    this.showLinkUrlInput = false;
    this.showDropdownLists = false;
    this.selectedSection = null;
  }
  // Generate field name based on label
  generateFieldName(label: string, _sectionTitle: string): string {
    // Remove special characters and spaces
    const cleanLabel = label.replace(/[^a-zA-Z0-9 ]/g, '');
    // Return the cleaned label
    return cleanLabel.replace(/ /g, ' ');
  }

  // Update field name when label changes
  updateFieldName(element: FormComponent, newLabel: string, sectionTitle: string): void {
    element.attributes.field_name = this.generateFieldName(newLabel, sectionTitle);
  }

  // Toggle required status
  toggleRequired(element: FormComponent): void {
    element.attributes.is_required = !element.attributes.is_required;
  }

  hoveredIndex: number | null = null;

  handleFlag(option: string, element: any): void {
    element.attributes.actions.flag = !element.attributes.actions.flag;
  }
  handleComment(option: string, element: any): void {
    element.attributes.actions.comment = !element.attributes.actions.comment;
  }
  handleImages(option: string, element: any): void {
    element.attributes.actions.camera = !element.attributes.actions.camera;
  }
  // Toggle multiselect status for dropdown
  toggleMultiselect(element: FormComponent): void {
    element.multiselect = !element.multiselect;
  }

  // Delete element from section
  deleteElement(section: FormSection, elementIndex: number): void {
    section.elements.splice(elementIndex, 1);
  }

    // Add a text field element to a section
  addTextFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Label";
    const textFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        placeholder_text: "Short Answer",
        show_label: true,
        validations: {

        },
        actions: {
          comment: true,
          camera: true,
          flag: true
        }
      }
    };
    this.selectedSection.elements.push(textFieldElement);
    this.hideElementSelectionSidebar();
  }

  // Add an email field element to a section
  addEmailFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Email";
    const emailFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        placeholder_text: "Email",
        show_label: true,
        validations: {
           pattern: "^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(emailFieldElement);
    this.hideElementSelectionSidebar();
  }

  // Add a number field element to a section
  addPhoneNumberFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Phone No.";
    const numberFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        placeholder_text: "Number",
        show_label: true,
        default_value: "+91",
        validations: {
          minlength: "10",
          maxlength: "13",
          pattern: "^(\\+91)?[0-9]{10}$"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(numberFieldElement);
    this.hideElementSelectionSidebar();
  }

  addNumberFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Number";
    const numberFieldElement: FormComponent = {
      type: "text",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        placeholder_text: "Number",
        show_label: true,
        validations: {
          pattern: "^s*[0-9]+s*$"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    }
    this.selectedSection.elements.push(numberFieldElement);
    this.hideElementSelectionSidebar();
  }

  addTextAreaFieldElement(): void {
    if (!this.selectedSection) return;

    const label = "Enter Your Label";
    const textFieldElement: FormComponent = {
      type: "textarea",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        placeholder_text: "Long Answer",
        show_label: true,
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(textFieldElement);
    this.hideElementSelectionSidebar();
  }

  addLocationElement(): void {
    if (!this.selectedSection) return;

    const label = "Select Location";
    const locationElement: FormComponent = {
      type: "map",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        show_label: true,
        default_lat: 28.6139,
        default_lng: 77.209,
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(locationElement);
    this.hideElementSelectionSidebar();
  }

  addDateFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Select Date";
    const dateFieldElement: FormComponent = {
      type: "date",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        show_label: true,
        validations: {
                  minDate: "",
                  maxDate: ""
        },
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    }
    this.selectedSection.elements.push(dateFieldElement);
    this.hideElementSelectionSidebar();
  }

  addTimeFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Select Time";
    const timeFieldElement: FormComponent = {
      type: "time",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        show_label: true,
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    }
    this.selectedSection.elements.push(timeFieldElement);
    this.hideElementSelectionSidebar();
  }

  addSignatureFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "signature";
    const signatureFieldElement: FormComponent = {
      type: "signature",
      attributes: {
        label: label,
        field_name: "signature",
        is_required: false,
        show_label: true,
        pen_color: "black",
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(signatureFieldElement);
    this.hideElementSelectionSidebar();
  }

  addImageElement(): void {
    if (!this.selectedSection) return;
    const label = "Upload Image";
    const imageElement: FormComponent = {
      type: "file",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        show_label: true,
        placeholder_text: "Upload Your Image",
        validations: {
          pattern: "\\.(jpg|jpeg|png)$",
          maxSize: "5"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(imageElement);
    this.hideElementSelectionSidebar();
  }
  // Toggle URL input and scroll to it
  toggleLinkUrlInput(event: Event): void {
    this.showLinkUrlInput = !this.showLinkUrlInput;

    if (this.showLinkUrlInput) {
      // Get the parent element (element-type-item)
      const target = event.currentTarget as HTMLElement;
      const parentElement = target.closest('.element-type-item');

      if (parentElement) {
        // Store reference to the element
        this.linkItemElement = parentElement;

        // Wait for the DOM to update before scrolling
        setTimeout(() => {
          parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    }
  }
  linkUrl: string = '';
  // Add a link element to the selected section
  addLinkElement(): void {
    if (this.selectedSection) {
      // Use the entered URL or a default if empty
      const url = this.linkUrl.trim() || '';
      const label = "Enter Your Label";
      const newElement: FormComponent = {
        type: 'link',
        attributes: {
          label: label,
          is_required: false,
          show_label: true,
          url: url,
          link_text: 'Click here',
          actions: {
            comment: false,
            camera: false,
            flag: false
          }
        }
      };
      this.selectedSection.elements.push(newElement);
      this.hideElementSelectionSidebar();
      this.linkUrl = '';
    }
  }

  // Toggle dropdown lists visibility
  toggleDropdownLists(): void {

    this.showDropdownLists = !this.showDropdownLists;
    if (this.showDropdownLists) {
      // Reset search when showing dropdown lists
      this.listSearchTerm = '';
      this.filteredFormLists = [...this.formLists];
    }
  }

  // Select a form list for dropdown
  selectFormList(list: DropdownData): void {
    this.selectedFormList = list;
    this.addDropdownElement(list);
  }

  // Add a dropdown element with the selected form list
  addDropdownElement(list: DropdownData): void {
    if (!this.selectedSection) return;

    const label = "Enter a Label";
    const dropdownElement: FormComponent = {
      type: "Select",
      multiselect: false,
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        placeholder_text: "Select",
        show_label: true,
        dataListId: list.id, // Store the actual list ID for reference
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(dropdownElement);
    this.hideElementSelectionSidebar();
  }

  addUserList():void{
    if (!this.selectedSection) return;

    // Fetch users from API and convert to DropdownData format
    this.coreDataService.GetUsers().subscribe({
      next: (users: any[]) => {
        // Check if users array is empty
        if (!users || users.length === 0) {
          this.shareService.showError('No users found in the system.');
          return;
        }
        // Convert users to DropdownData format
         const usersDropdownData = this.convertUsersToDropdownData(users);

        const label = "Select User";
        const dropdownElement: FormComponent = {
          type: "Select",
          multiselect: false,
          attributes: {
            label: label,
            field_name: this.generateFieldName(label, this.selectedSection?.title || ''),
            is_required: false,
            placeholder_text: "Select Users",
            show_label: true,
            dataSource: usersDropdownData, // Set the converted user data
            actions: {
              comment: false,
              camera: false,
              flag: false
            }
          }
        };

        // Add the dropdown element to the selected section
        this.selectedSection?.elements.push(dropdownElement);
        this.hideElementSelectionSidebar();

        // Show success message
        this.shareService.showSuccess('User dropdown added successfully!');
      },
      error: (error) => {
        console.error('Error fetching users:', error);
        this.shareService.showError('Failed to fetch users. Please try again.');
      }
    });
  }

  // convert User to List
convertUsersToDropdownData(users: any[]): DropdownData {
  return {
    auditHistory: {},
    list: [
      {
        items: users.map(user => {
          const userName = user.details?.name ||
                           user.name ||
                           user.firstName ||
                           user.username ||
                           `User ${user.id}`;

          return { value: userName };
        })
      }
    ]
  };
}


   // Toggle guide image upload input
   toggleGuideImageUpload(event: Event): void {
    this.showGuideImageUpload = !this.showGuideImageUpload;

    if (this.showGuideImageUpload) {
      // Get the parent element (element-type-item)
      const target = event.currentTarget as HTMLElement;
      const parentElement = target.closest('.element-type-item');

      if (parentElement) {
        // Wait for the DOM to update before scrolling
        setTimeout(() => {
          parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    }
  }
  imagePath: string = '';
  // Handle guide image upload
  onGuideImageSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input?.files?.length) {
      const file = input.files[0];

      // Validate file type
      const allowedExtensions = new RegExp("\\.(jpg|jpeg|png)$", "i");
      if (!allowedExtensions.test(file.name)) {
        console.error('Invalid file type. Only JPG, JPEG, and PNG are allowed.');
        return;
      }

      // Validate file size (max 5MB)
      const maxSizeMB = 5;
      if (file.size / 1024 / 1024 > maxSizeMB) {
        console.error(`File size must be less than ${maxSizeMB} MB.`);
        return;
      }

      // Preview the uploaded image
      this.guideImageUrl = URL.createObjectURL(file);

      // Upload the image to the server
      this.coreDataService.saveImage(file).subscribe({
        next: (res) => {
          this.imagePath = res.imagePath; // e.g., "uploads/image123.png"
          console.log('Image uploaded successfully:', res.imagePath);
        },
        error: (err) => {
          console.error('Image upload failed:', err);
        }
      });
    }
  }

  // Add guide image element with the uploaded image
  addGuideImageElement(): void {
    if (!this.selectedSection) return;

    const label = "For Your Guidance";
    const imageElement: FormComponent = {
      type: "image",
      attributes: {
        label: label,
        field_name: 'Guidance Image',
        image_url: this.imagePath || "",
        show_label: true,
        alt_text: "Guide to fill the form",
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(imageElement);
    this.hideElementSelectionSidebar();
  }

  addQRScannerElement(){
    if (!this.selectedSection) return;
    const label = "QR Scanner";
    const qrScannerElement: FormComponent = {
      type: "qrscanner",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        show_label: true,
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(qrScannerElement);
    this.hideElementSelectionSidebar();
  }

  addPDFFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "Upload PDF";
    const pdfElement: FormComponent = {
      type: "PDFfile",
      attributes: {
        label: label,
        field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        show_label: true,
        placeholder_text: "Upload Your PDF",
        validations: {
          maxSize: "5"
        },
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(pdfElement);
    this.hideElementSelectionSidebar();
  }

  togglePdfUpload(event: Event):void{
    this.showPdfUpload = !this.showPdfUpload;
    if (this.showPdfUpload) {
      // Get the parent element (element-type-item)
      const target = event.currentTarget as HTMLElement;
      const parentElement = target.closest('.element-type-item');

      if (parentElement) {
        // Wait for the DOM to update before scrolling
        setTimeout(() => {
          parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    }
  }

  pdfComponent: string = '';
  onPdfSelect(event: Event){
      const input = event.target as HTMLInputElement;

  if (input.files && input.files.length > 0) {
    const file = input.files[0];

    const reader = new FileReader();

    reader.onload = () => {
      const base64String = (reader.result as string);

      // Convert base64 to Blob structure
        this.pdfComponent = base64String;
    };

    reader.readAsDataURL(file); // This reads the PDF as base64 string
  }
  }

  AddviewPDFFieldElement(): void {
    if (!this.selectedSection) return;
    const label = "View PDF";
    const pdfElement: FormComponent = {
      type: "pdfviewer",
      attributes: {
        label: label,
        // field_name: this.generateFieldName(label, this.selectedSection.title),
        is_required: false,
        pdf_url: this.pdfComponent ||"",
        show_label: true,
        actions: {
          comment: false,
          camera: false,
          flag: false
        }
      }
    };
    this.selectedSection.elements.push(pdfElement);
    this.hideElementSelectionSidebar();
  }

  // Select a form from the sidebar
  selectForm(form: FormJson): void {
    this.selectedFormId = form.id || null;
    this.loadFormForEditing(form);
  }

  // Load an existing form for editing
  loadFormForEditing(form: FormJson): void {
    this.formData = { ...form }; // Create a copy to avoid modifying the original
    // Update the form builder form with the loaded data
    this.formBuilderForm.patchValue({
      formName: form.auditHistory.formName || 'Untitled Form',
      userName: form.auditHistory.userName || 'Unknown User',
    });

  }

  // Start creating a new form
  startNewForm(): void {
    this.showCreateFormModal = true;
  }

  // Select element for validation editing
  selectElementForValidation(element: FormComponent, section: FormSection): void {
    this.selectedElement = element;
    this.selectedElementSection = section;
  }

  // Clear selected element
  clearSelectedElement(): void {
    this.selectedElement = null;
    this.selectedElementSection = null;
  }

  // Update validation for selected element
  updateValidation(validationType: string, value: any): void {
    if (!this.selectedElement) return;

    if (!this.selectedElement.attributes.validations) {
      this.selectedElement.attributes.validations = {};
    }

    if (value === '' || value === null || value === undefined) {
      delete this.selectedElement.attributes.validations[validationType];
    } else {
      this.selectedElement.attributes.validations[validationType] = value;
    }
  }

  // Handle input events for validation updates
  onValidationInputChange(event: Event, validationType: string): void {
    const target = event.target as HTMLInputElement;
    this.updateValidation(validationType, target.value);
  }

  // Handle checkbox events for validation updates
  onValidationCheckboxChange(event: Event, validationType: string): void {
    const target = event.target as HTMLInputElement;
    this.updateValidation(validationType, target.checked ? 'true' : '');
  }

  // Get validation value for selected element
  getValidationValue(validationType: string): any {
    if (!this.selectedElement || !this.selectedElement.attributes.validations) {
      return '';
    }
    return this.selectedElement.attributes.validations[validationType] || '';
  }

  // Check if validation type is applicable for current element type
  isValidationApplicable(validationType: string): boolean {
    if (!this.selectedElement) return false;

    const elementType = this.selectedElement.type;
    const elementLabel = this.selectedElement.attributes.label?.toLowerCase() || '';

    // Check if this is an email field (either by type or label)
    const isEmailField = elementType === 'email' || elementLabel.includes('email');

    switch (validationType) {
      case 'minlength':
      case 'maxlength':
        // Email fields should not show length validation
        if (isEmailField) return false;
        return ['text', 'textarea'].includes(elementType);
      case 'min':
      case 'max':
        return elementType === 'number';
      case 'minDate':
      case 'maxDate':
      case 'noFuture':
      case 'noPast':
        return elementType === 'date';
      case 'maxSize':
        return elementType === 'file';
      default:
        return true;
    }
  }
}

