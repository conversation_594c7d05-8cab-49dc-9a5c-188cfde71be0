import { Component, OnInit, AfterViewInit } from '@angular/core';
import { CoreDataService } from '../../core-data.service';
import { FormJson, FormSubmission } from '../Model/model';
import { ShareService } from '../../SharedData/share-services.service';

@Component({
  selector: 'app-forms',
  templateUrl: './../Template/Forms.component.html',
  styleUrl: './../Style/forms-component.css'
})
export class FormsComponent implements OnInit, AfterViewInit {
  formsList: FormJson[] = [];
  selectedFormId: string | null = null;
  selectedForm: FormJson | null = null;
  isSidebarCollapsed: boolean = false;
  groupedSubmissions: { [date: string]: any[] } = {}; // Group submissions by date
  // Track the current view state of the sidebar
  sidebarView: 'forms' | 'types' | 'submissions' = 'forms';

  // Store form submissions
  formSubmissions: FormSubmission[] = [];
  selectedSubmissionId: string | null = null;

  // Store the current form for which types are being shown
  currentFormForTypes: FormJson | null = null;



  // Mock form types for demonstration
  formTypes: string[] = [
    'New',
    // 'In Progress',
    // 'Duplicate',
    // 'Revision',
    'Previously Submitted'
  ];

  constructor(
    private coreDataService: CoreDataService,
    public shareService: ShareService
  ) {}

  ngAfterViewInit() {
    // Component initialization complete
  }



  ngOnInit() {
    this.sidebarView = 'forms';
    this.formsList = this.shareService.FromList;

    // Subscribe to sidebar view changes
    this.shareService.sidebarViewChange$.subscribe(view => {
      this.sidebarView = view;
    });




  }

  // loadForms() {
  //   this.coreDataService.GetFORMbyLoc(this.shareService.selectedLocation).subscribe({
  //     next: (data) => {
  //       this.formsList = data;
  //       this.shareService.FromList;
  //     },
  //     error: (err) => {
  //       console.error('Error loading forms:', err);
  //     }
  //   });
  // }

  selectForm(form: any) {
    if (form && form.id) {
      // Switch to the types view and store the current form
      this.sidebarView = 'types';
      this.currentFormForTypes = form;
      this.selectedForm = form;
      this.loadFormSubmissions(form.id);

    } else {
      console.error('Invalid form object or missing id:', form);
    }
  }

  selectFormType(formType: string) {
    // Set the selected form ID and load the form
    if (this.currentFormForTypes && this.currentFormForTypes.id) {
      if (formType === 'Previously Submitted') {
        // Switch to submissions view immediately to show loading state
        this.sidebarView = 'submissions';
        // this.loadFormSubmissions(this.currentFormForTypes.id);
      } else if (formType === 'New') {
        // For new form, we need to reset everything
        this.selectedSubmissionId = null;
        setTimeout(() => {
          if (this.selectedFormId === this.currentFormForTypes?.id) {
            this.selectedFormId = null;
            setTimeout(() => {
              this.selectedFormId = this.currentFormForTypes?.id || null;
              console.log('New form selected with ID:', this.selectedFormId);
            }, 50);
          } else {
            this.selectedFormId = this.currentFormForTypes?.id || null;
          }
        }, 50);
      }
    }
  }

  loadFormSubmissions(formId: string, openInHtmlView: boolean = false) {
    // Clear previous submissions before loading new ones
    this.formSubmissions = [];

    this.coreDataService.getFormDatabyTemplateId(formId).subscribe({
      next: (data: any) => {
        if (data && Array.isArray(data) && data.length > 0) {
          this.formSubmissions = data;

          // console.log('Form submissions loaded:', this.formSubmissions.length, 'submissions found');

          // If openInHtmlView is true and we have a selected submission ID, find and open that submission
          if (openInHtmlView && this.selectedSubmissionId) {
            const selectedSubmission = data.find((submission: FormSubmission) =>
              submission.id === this.selectedSubmissionId);

            if (selectedSubmission) {
              // Open the submission in HTML view mode
              this.selectSubmission(selectedSubmission, true);
            }
          }
        } else {
          // Handle empty or invalid response
          this.formSubmissions = [];
          console.log('No form submissions found for form ID:', formId);
        }
      },
      error: (err) => {
        console.error('Error loading form submissions:', err);
        // Keep the empty array to show the "No data submitted" message
        this.formSubmissions = [];
      }
    });
  }

  selectSubmission(submission: FormSubmission, openInHtmlView: boolean = false) {
    if (submission && submission.id) {
      if (submission.formTemplateId) {
        this.selectedFormId = submission.formTemplateId;
      }

      setTimeout(() => {
        this.selectedSubmissionId = submission.id || null;

        // If openInHtmlView is true, automatically open the form in HTML view mode
        if (openInHtmlView) {
          // Wait for the form to load completely
          setTimeout(() => {
            // Use the ShareService to trigger full window mode
            console.log('Requested to open form in HTML view mode');
          }, 500);
        }
      }, 100);
    }
  }

  backToForms() {
    // Go back to the forms list view
    this.sidebarView = 'forms';
    this.selectedSubmissionId = null;
    this.selectedFormId = null;
    this.isSidebarCollapsed = false;
  }

  backToFormTypes() {
    // Go back to the form types view
    this.sidebarView = 'types';
    this.selectedSubmissionId = null;
    this.selectedFormId = null;
  }

  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }





  sortByDate = (a: any, b: any) => {
    // Since we're now using ISO date strings (YYYY-MM-DD), we can compare them directly
    // or convert to Date objects for proper comparison
    return new Date(b.key).getTime() - new Date(a.key).getTime(); // Latest first
  };





  formViewLink(formId: string, submissionId: string){

     const linkData = {
          formId: formId,
          submissionId: submissionId,
          timestamp: new Date().getTime(),
          directHtmlView: true // Flag to indicate direct HTML view
        };

    // Convert to JSON and encode with Base64
        const jsonData = JSON.stringify(linkData);
        const encodedData = btoa(encodeURIComponent(jsonData));

        const baseUrl = window.location.origin;
        const shareableLink = `${baseUrl}/form-preview?data=${encodedData}`;

        window.open(shareableLink, "_blank");
  }

}
