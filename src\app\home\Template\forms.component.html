<div class="forms-container">


  <!-- Mobile View: Only Show One Panel at a Time -->
<div *ngIf="isMobileView">
  <div *ngIf="mobileView === 'forms'">
    <!-- Copy existing sidebar content here -->
    <ng-container *ngTemplateOutlet="sidebarTemplate"></ng-container>
  </div>
  <div *ngIf="mobileView === 'types'">
    <ng-container *ngTemplateOutlet="mainContentTemplate"></ng-container>
  </div>
</div>

<!-- Desktop View: Show Both -->
<div *ngIf="!isMobileView" class="forms-container">
  <ng-container *ngTemplateOutlet="sidebarTemplate"></ng-container>
  <ng-container *ngTemplateOutlet="mainContentTemplate"></ng-container>
</div>


  <!-- Sidebar -->
   <ng-template #sidebarTemplate>
  <div class="sidebar" [class.collapsed]="isSidebarCollapsed">
    <!-- Forms List View -->
    <div *ngIf="sidebarView === 'forms'">
      <div class="sidebar-header">
        <h3>Forms</h3>
      </div>
      <div class="sidebar-content">
        <ul class="forms-list">
          <li *ngFor="let form of this.shareService.FromList" class="form-item">
            <div class="form-header"
                 [class.active]="selectedFormId === form.id"
                 (click)="selectForm(form)">
              <span class="form-name">{{ form.auditHistory.formName || 'Unnamed Form' }}</span>
              <i class="fas fa-chevron-right"></i>
            </div>
          </li>
        </ul>
        <div *ngIf="this.shareService.FromList.length === 0" class="no-forms">
          No forms available
        </div>
      </div>
    </div>

    <!-- Form Types View -->
    <div *ngIf="sidebarView === 'types'">
      <div class="sidebar-header">
        <button class="back-button" (click)="backToForms()">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h3>{{ currentFormForTypes?.auditHistory?.formName || 'Form Types' }}</h3>
      </div>
      <div class="sidebar-content">
        <ul class="form-types-list">
          <li *ngFor="let type of formTypes"
              class="form-type-item"
              (click)="selectFormType(type)">
            <span class="form-type-name">{{ type }}</span>
            <i class="fas fa-chevron-right" style="color: lightgray;"></i>
          </li>
        </ul>
      </div>
    </div>

    <!-- Form Submissions View -->
    <div *ngIf="sidebarView === 'submissions'">
      <div class="sidebar-header">
        <button class="back-button" (click)="backToFormTypes()">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h3>Submitted Forms</h3>
      </div>
      <div class="sidebar-content">
        <ul class="submissions-list">
          <li *ngFor="let submission of formSubmissions"
              class="submission-item"
              [class.active]="selectedSubmissionId === submission.id"
              (click)="selectSubmission(submission)">
            <div class="submission-header">
              <!-- <span class="submission-id">ID: {{ submission.id }}</span> -->
              <span class="submission-date">{{ submission.auditHistory.updatedDate | date:'short' }}</span>
            </div>
            <div class="submission-info">
              <span class="submission-by">By: {{ submission.auditHistory.updatedBy || 'Dev One' }}</span>
            </div>
          </li>
        </ul>
        <div *ngIf="formSubmissions.length === 0" class="no-submissions">
          <i class="fas fa-info-circle"></i>
          No data submitted for this form
        </div>
      </div>
    </div>


  </div></ng-template>
  <!-- Main Content -->
   <ng-template #mainContentTemplate>
  <div class="main-content" [class.expanded]="isSidebarCollapsed">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" (click)="toggleSidebar()">
      <i class="fas" [ngClass]="{'fa-chevron-right': isSidebarCollapsed, 'fa-chevron-left': !isSidebarCollapsed}"></i>
    </button>

    <!-- Form Content -->
    <div *ngIf="selectedFormId" class="form-details">
      <!-- Pass both formId and submissionId (if available) to the home component -->
      <app-home
        [formId]="selectedFormId || ''"
        [submissionId]="selectedSubmissionId || ''">
      </app-home>
    </div>
    <div *ngIf="!selectedFormId" class="no-selection">
      <p>Select a form from the sidebar to view details</p>
    </div>
  </div></ng-template>


  <!-- Mobile Bottom Navigation -->
<div class="bottom-nav" *ngIf="isMobileView">
  <button class="nav-button" [class.active]="mobileView === 'forms'" (click)="mobileView = 'forms'">
    <i class="fas fa-list"></i>
    <span>Forms</span>
  </button>
  <button class="nav-button" [class.active]="mobileView === 'types'" [disabled]="!selectedFormId" (click)="mobileView = 'formDetails'">
    <i class="fas fa-file-alt"></i>
    <span>Details</span>
  </button>
</div>

</div>
